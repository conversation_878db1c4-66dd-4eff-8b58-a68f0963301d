/**
 * MediaBar Demo Component
 * Demonstrates the image upload and content replacement functionality
 */

import React, { useState } from "react";
import { Card, Typography, Divider } from "antd";
import MediaBar from "./MediaBar";

const { Title, Text, Paragraph } = Typography;

const MediaBarDemo = () => {
  // Sample form data with content that has image placeholders
  const [formData, setFormData] = useState({
    contentJSON: {
      "Hero Section": {
        hero_image: "", // This will be auto-filled when hero.png is uploaded
        hero_title: "Welcome to Our Website",
        hero_subtitle: "Amazing content awaits you"
      },
      "About Section": {
        about_image: "", // This will be auto-filled when about.png is uploaded
        about_title: "About Us",
        about_content: "We are a great company"
      },
      "Gallery": {
        gallery_image1: "", // This will be auto-filled when gallery1.png is uploaded
        gallery_image2: "", // This will be auto-filled when gallery2.png is uploaded
        gallery_image3: ""  // This will be auto-filled when gallery3.png is uploaded
      }
    }
  });

  const [fileList, setFileList] = useState([]);

  // Sample HTML content with image placeholders
  const sampleHTML = `
    <div class="hero-section">
      <img src="\${hero_image}" alt="\${hero_image}" class="hero-img" />
      <h1>\${hero_title}</h1>
      <p>\${hero_subtitle}</p>
    </div>
    
    <div class="about-section">
      <img src="\${about_image}" alt="\${about_image}" class="about-img" />
      <h2>\${about_title}</h2>
      <p>\${about_content}</p>
    </div>
    
    <div class="gallery">
      <img src="\${gallery_image1}" alt="\${gallery_image1}" class="gallery-img" />
      <img src="\${gallery_image2}" alt="\${gallery_image2}" class="gallery-img" />
      <img src="\${gallery_image3}" alt="\${gallery_image3}" class="gallery-img" />
    </div>
  `;

  return (
    <div className="tw-p-6 tw-max-w-7xl tw-mx-auto">
      <Title level={2}>MediaBar Image Upload & Content Replacement Demo</Title>
      
      <Paragraph>
        This demo shows how the MediaBar component automatically matches uploaded images 
        with content keys and replaces placeholders in HTML content.
      </Paragraph>

      <div className="tw-grid tw-grid-cols-1 lg:tw-grid-cols-2 tw-gap-6">
        {/* MediaBar Component */}
        <Card title="Media Manager" className="tw-h-fit">
          <div style={{ height: "500px" }}>
            <MediaBar
              fileList={fileList}
              setFileList={setFileList}
              formData={formData}
              setFormData={setFormData}
            />
          </div>
        </Card>

        {/* Content JSON Display */}
        <div className="tw-space-y-4">
          <Card title="Content JSON (Auto-Updated)" size="small">
            <pre className="tw-text-xs tw-bg-gray-50 tw-p-3 tw-rounded tw-overflow-auto tw-max-h-64">
              {JSON.stringify(formData.contentJSON, null, 2)}
            </pre>
          </Card>

          <Card title="Sample HTML Template" size="small">
            <pre className="tw-text-xs tw-bg-gray-50 tw-p-3 tw-rounded tw-overflow-auto tw-max-h-64">
              {sampleHTML}
            </pre>
          </Card>
        </div>
      </div>

      <Divider />

      <div className="tw-space-y-4">
        <Title level={3}>How It Works:</Title>
        
        <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-4">
          <Card size="small" title="1. Upload Images">
            <Text>
              Upload images with descriptive names like "hero.png", "about.jpg", "gallery1.png"
            </Text>
          </Card>

          <Card size="small" title="2. Auto-Matching">
            <Text>
              The system automatically matches image names (without extension) to content keys
            </Text>
          </Card>

          <Card size="small" title="3. Content Update">
            <Text>
              Content JSON is automatically updated with image paths, and HTML placeholders are replaced
            </Text>
          </Card>

          <Card size="small" title="4. Manual Assignment">
            <Text>
              Use the "Assign to Content" dropdown to manually link images to specific content keys
            </Text>
          </Card>

          <Card size="small" title="5. Alt Attributes">
            <Text>
              Alt attributes are automatically generated from image names for better accessibility
            </Text>
          </Card>

          <Card size="small" title="6. Live Preview">
            <Text>
              Changes are reflected immediately in the template preview with proper image src and alt tags
            </Text>
          </Card>
        </div>
      </div>

      <Divider />

      <Card title="Test Instructions" className="tw-bg-blue-50">
        <div className="tw-space-y-2">
          <Text strong>To test the functionality:</Text>
          <ol className="tw-list-decimal tw-list-inside tw-space-y-1">
            <li>Upload an image named "hero.png" - it should auto-assign to hero_image</li>
            <li>Upload an image named "about.jpg" - it should auto-assign to about_image</li>
            <li>Upload any image and manually assign it using the dropdown</li>
            <li>Check the Content JSON to see the automatic updates</li>
            <li>The template preview will show the images with proper src and alt attributes</li>
          </ol>
        </div>
      </Card>
    </div>
  );
};

export default MediaBarDemo;
