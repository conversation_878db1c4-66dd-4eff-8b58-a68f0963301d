/**
 * useMediaManager Hook
 * Handles media file operations including upload, delete, and state management
 */

import { useState, useCallback, useEffect } from 'react';
import { message } from 'antd';
import Services from '../util/API/service';
import { CONSTANTS } from '../util/constant/CONSTANTS';

const useMediaManager = () => {
  const [mediaFiles, setMediaFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(false);

  // Initialize with sample data (remove when API is ready)
  useEffect(() => {
    const sampleData = [
      {
        id: 1,
        filename: "hero_content_image1.png",
        originalName: "hero_content_image1.png",
        path: "/uploads/hero_content_image1.png",
        size: 245760,
        uploadDate: new Date().toISOString(),
        type: "image/png"
      },
      {
        id: 2,
        filename: "content_image1.png", 
        originalName: "content_image1.png",
        path: "/uploads/content_image1.png",
        size: 189440,
        uploadDate: new Date().toISOString(),
        type: "image/png"
      },
      {
        id: 3,
        filename: "content_image2.png",
        originalName: "content_image2.png", 
        path: "/uploads/content_image2.png",
        size: 156672,
        uploadDate: new Date().toISOString(),
        type: "image/png"
      },
      {
        id: 4,
        filename: "content_image3.png",
        originalName: "content_image3.png",
        path: "/uploads/content_image3.png", 
        size: 203520,
        uploadDate: new Date().toISOString(),
        type: "image/png"
      }
    ];
    setMediaFiles(sampleData);
  }, []);

  // Upload file to server
  const uploadFile = useCallback(async (file) => {
    setUploading(true);
    
    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('image', file);
      
      // For now, simulate upload. Uncomment below for real API call
      /*
      const response = await Services.post(CONSTANTS.API.upload.image.endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      const uploadedFile = {
        id: Date.now(),
        filename: response.data.filename,
        originalName: response.data.originalName,
        path: response.data.path,
        size: response.data.size,
        uploadDate: new Date().toISOString(),
        type: file.type
      };
      */
      
      // Simulate upload response
      const uploadedFile = {
        id: Date.now(),
        filename: `${Date.now()}-${file.name}`,
        originalName: file.name,
        path: `/uploads/${Date.now()}-${file.name}`,
        size: file.size,
        uploadDate: new Date().toISOString(),
        type: file.type
      };
      
      // Add to state
      setMediaFiles(prev => [...prev, uploadedFile]);
      message.success('File uploaded successfully');
      
      return uploadedFile;
      
    } catch (error) {
      console.error('Upload error:', error);
      message.error('Failed to upload file');
      throw error;
    } finally {
      setUploading(false);
    }
  }, []);

  // Delete file
  const deleteFile = useCallback(async (fileId) => {
    try {
      // For now, just remove from state. Add API call when ready
      /*
      await Services.delete(`/api/media/${fileId}`);
      */
      
      setMediaFiles(prev => prev.filter(file => file.id !== fileId));
      message.success('File deleted successfully');
      
    } catch (error) {
      console.error('Delete error:', error);
      message.error('Failed to delete file');
      throw error;
    }
  }, []);

  // Get file by ID
  const getFileById = useCallback((fileId) => {
    return mediaFiles.find(file => file.id === fileId);
  }, [mediaFiles]);

  // Get files by type
  const getFilesByType = useCallback((type) => {
    return mediaFiles.filter(file => file.type?.startsWith(type));
  }, [mediaFiles]);

  // Search files
  const searchFiles = useCallback((searchTerm) => {
    if (!searchTerm.trim()) return mediaFiles;
    
    return mediaFiles.filter(file => 
      file.originalName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      file.filename.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [mediaFiles]);

  // Format file size
  const formatFileSize = useCallback((bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  // Get file URL for display
  const getFileUrl = useCallback((file) => {
    // In production, this would be the full URL to the file
    return `${import.meta.env.VITE_API_URL || 'http://localhost:3543'}${file.path}`;
  }, []);

  return {
    mediaFiles,
    uploading,
    loading,
    uploadFile,
    deleteFile,
    getFileById,
    getFilesByType,
    searchFiles,
    formatFileSize,
    getFileUrl
  };
};

export default useMediaManager;
