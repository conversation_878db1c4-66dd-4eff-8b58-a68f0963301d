import React, { useState, useCallback, useEffect } from "react";
import {
  Button,
  Input,
  Upload,
  Card,
  Row,
  Col,
  Popconfirm,
  Typography,
  Empty,
  Spin,
} from "antd";
import { Search, Image as ImageIcon, File } from "lucide-react";
import { UploadOutlined, DeleteOutlined } from "@ant-design/icons";
import useMediaManager from "../../../hooks/useMediaManager";
import SearchBar from "../../common/SearchBar";

const { Search: AntSearch } = Input;
const { Text, Title } = Typography;

const MediaBar = ({ fileList, setFileList, setFormData, formData }) => {
  const {
    mediaFiles,
    uploading,
    uploadFile,
    deleteFile,
    searchFiles,
    formatFileSize,
  } = useMediaManager();

  const [searchTerm, setSearchTerm] = useState("");
  // const [filteredFiles, setFilteredFiles] = useState([]);

  // Update filtered files when mediaFiles or searchTerm changes
  useEffect(() => {
    setFileList(searchFiles(searchTerm));
  }, [mediaFiles, searchTerm, searchFiles]);

  // Handle search input
  const handleSearch = useCallback((value) => {
    setSearchTerm(value);
  }, []);

  // Handle file upload
  const handleUpload = async (file) => {
    try {
      await uploadFile(file);
    } catch (error) {
      // Error handling is done in the hook
    }
    return false; // Prevent default upload behavior
  };

  // Handle file deletion
  const handleDelete = async (fileId) => {
    try {
      await deleteFile(fileId);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  return (
    <div className="tw-h-full tw-flex tw-flex-col tw-bg-white">
      {/* Header Section */}
      <div className="tw-p-4 tw-border-b tw-border-gray-200">
        <div className="tw-flex tw-items-center tw-justify-between tw-mb-4">
          <Title level={4} className="!tw-m-0 tw-flex tw-items-center">
            Media
          </Title>

          <Upload
            beforeUpload={handleUpload}
            showUploadList={false}
            accept="image/*"
            disabled={uploading}
          >
            <Button
              type="primary"
              size="large"
              title=""
              loading={uploading}
              className="tw-px-6 tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
            >
              Upload
            </Button>
          </Upload>
        </div>

        {/* Search Bar */}
        <SearchBar handleSearch={(e) => handleSearch(e)} />
      </div>

      {/* Media Files List */}
      <div className="tw-flex-1 tw-overflow-auto tw-p-4">
        {uploading && (
          <div className="tw-text-center tw-py-4">
            <Spin size="large" />
            <Text className="tw-block tw-mt-2 tw-text-gray-500">
              Uploading...
            </Text>
          </div>
        )}

        {fileList?.length === 0 && !uploading ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              searchTerm
                ? `No files found matching "${searchTerm}"`
                : "No media files uploaded yet"
            }
            className="tw-py-8"
          >
            {!searchTerm && (
              <Upload
                beforeUpload={handleUpload}
                showUploadList={false}
                accept="image/*"
                multiple
                disabled={uploading}
              >
                <Button type="primary" icon={<UploadOutlined />}>
                  Upload Your First File
                </Button>
              </Upload>
            )}
          </Empty>
        ) : (
          <Row gutter={[16, 16]}>
            {fileList?.map((file) => (
              <Col xs={24} sm={12} md={12} lg={12} key={file.id}>
                <Card
                  className="tw-h-full tw-border tw-border-gray-200 hover:tw-border-blue-300 tw-transition-all tw-duration-200"
                  styles={{ body: { padding: "4px 8px" } }}
                >
                  <div className="tw-text-center">
                    {/* File Info */}
                    <div className="tw-space-y-1 tw-flex tw-items-center tw-justify-between">
                      <Text
                        className="tw-block tw-text-sm tw-font-medium tw-text-gray-900 tw-truncate"
                        title={file.originalName}
                      >
                        {file.originalName}
                      </Text>
                      <Popconfirm
                        key="delete"
                        title="Delete this file?"
                        description="This action cannot be undone."
                        onConfirm={() => handleDelete(file.id)}
                        okText="Delete"
                        cancelText="Cancel"
                        okButtonProps={{ danger: true }}
                      >
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          className="tw-w-full"
                        />
                      </Popconfirm>
                      {/* <Text className="tw-block tw-text-xs tw-text-gray-500">
                        {formatFileSize(file.size)}
                      </Text> */}
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        )}
      </div>
    </div>
  );
};

export default MediaBar;
