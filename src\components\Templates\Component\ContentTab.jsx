import { Button, Collapse, Input, Row, Col, message } from "antd";
import {
  ChevronDown,
  ChevronUp,
  Upload as UploadIcon,
  ExpandIcon,
  Minimize2,
  ListCollapse,
  Image,
} from "lucide-react";
import { useState, useEffect, useMemo, useRef, useCallback } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import TemplatePreview from "./TemplatePreview";
import SearchBar from "../../common/SearchBar";
import JsonContentCollapse from "../../common/JsonContentCollapse";
import ContentCollapseBar from "./ContentCollapseBar";
import useDebounce from "../../../hooks/useDebounce";
import TabList from "../../common/TabList";
import MediaBar from "./MediaBar";

const { Panel } = Collapse;
const { TextArea } = Input;

// "Hero Section": {
//   $section_title: "Your Adventure Awaits!",
//   $section_content:
//     "Discover and book bus tickets to your favorite destinations with ease. We offer the best prices and a comfortable journey.",
// },
// Popular_Bus_Routes: [
//   {
//     $title: "Coimbatore Buses",
//     $location: "Manali, Jaipur",
//   },
//   {
//     $title: "Mountain Express",
//     $location: "Shimla, Ooty",
//   },
//   {
//     $title: "Coastal Rider",
//     $location: "Goa, Pondicherry",
//   },
// ],

const ContentTab = ({
  formData,
  setFormData,
  onCancel,
  handleSubmit,
  saving,
  pages,
  components = [],
}) => {
  const [contentJSON, setContentJSON] = useState(formData?.contentJSON ?? {});
  const [fileList, setFileList] = useState(formData?.FileList ?? []);

  const [previewMode, setPreviewMode] = useState("contents");

  const tabContents = {
    contents: {
      key: "contents",
      label: "Content",
      icon: <ListCollapse className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <ContentCollapseBar
          contentJSON={contentJSON}
          setContentJSON={setContentJSON}
          saving={saving}
          formData={formData}
          pages={pages}
        />
      ),
    },
    media: {
      key: "media",
      label: "Media",
      icon: <Image className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: (
        <MediaBar
          formData={formData}
          setFormData={setFormData}
          fileList={fileList}
          setFileList={setFileList}
        />
      ),
    },
  };
  // Local content JSON state (example data for testing functionality)
  const [placeholder, setPlaceholder] = useState("");

  useEffect(() => {
    setPlaceholder(formData?.placeholders || []);
    if (formData?.pages?.length > 0) {
      setContentJSON((prev) => {
        let keydata = {};

        formData.pages.forEach((page) => {
          if (page?.pagePlaceHolder?.length > 0) {
            let categoryData = {};

            page.pagePlaceHolder.forEach((category) => {
              let placeholdersData = {};

              // Normal placeholders
              category?.placeholders?.forEach((ph) => {
                placeholdersData[ph] = "";
              });

              // Handle repeated component
              if (
                category?.repeatedComponentId &&
                category?.repeatedComponent
              ) {
                const repeatedKey = category?.repeatedComponentName?.[0];
                if (repeatedKey) {
                  const repeatedPlaceholders =
                    category.repeatedComponent.placeholders || [];

                  // initialize with a single empty item; user can add more dynamically
                  placeholdersData[repeatedKey] = [
                    repeatedPlaceholders.reduce((acc, ph) => {
                      acc[ph] = "";
                      return acc;
                    }, {}),
                  ];
                }
              }

              categoryData[category?.categoryName] = placeholdersData;
            });

            keydata[page?.name] = categoryData;
          }
        });

        console.log("Final keydata:", keydata);

        return {
          ...prev,
          ...keydata,
        };
      });
    }
  }, []);

  useEffect(() => {
    setContentJSON((prev) => ({
      ...prev,
      ...formData?.contentJSON,
    }));
  }, [formData?.contentJSON]);

  // Debounced contentJSON for preview updates (500ms delay)
  const debouncedContentJSON = useDebounce(contentJSON, 500);

  const handleSaveContent = async () => {
    try {
      // Update formData with generated content
      const updatedFormData = {
        ...formData,
        // full_template_content: fullTemplateContent,
        // templateComponentList: templateComponentList,
        contentJSON: contentJSON || [],
      };
      handleSubmit(updatedFormData);
    } catch (error) {
      console.error("Error generating template structure:", error);
      message.error("Failed to generate template structure. Please try again.");
    }
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden">
        <TemplatePreview
          isPageLibraryOpen={true}
          //   setIsPageLibraryOpen={setIsPageLibraryOpen}
          isTemplateStructureOpen={true}
          //   setIsTemplateStructureOpen={() => {}} // Disable toggle
          formData={formData}
          setFormData={setFormData}
          pages={pages}
          components={components}
          handleSave={handleSaveContent}
          onCancel={onCancel}
          saving={saving}
          isDrop={false} // Disable drop functionality
          contentJSON={debouncedContentJSON}
        />

        {/* Right Side - Content Editor */}
        <div className="tw-h-full tw-w-full tw-flex tw-flex-col tw-bg-white tw-flex-1">
          <TabList
            tabContents={tabContents}
            setPreviewMode={setPreviewMode}
            previewMode={previewMode}
          />
          <div className="tw-space-y-4">
            {tabContents[previewMode]?.content ? (
              tabContents[previewMode]?.content
            ) : (
              <></>
            )}
          </div>
        </div>
      </div>
    </DndProvider>
  );
};

export default ContentTab;
